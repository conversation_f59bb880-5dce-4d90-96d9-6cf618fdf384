package services.oneteam.ai.shared.domains.workspace

import org.jetbrains.exposed.dao.id.EntityID
import org.jetbrains.exposed.sql.*
import org.slf4j.Logger
import org.slf4j.LoggerFactory
import services.oneteam.ai.shared.Checks
import services.oneteam.ai.shared.domains.Page
import services.oneteam.ai.shared.domains.PageRequest
import services.oneteam.ai.shared.domains.SortableFields
import services.oneteam.ai.shared.domains.tenant.Tenant
import services.oneteam.ai.shared.domains.user.User
import services.oneteam.ai.shared.domains.user.UserSchema
import services.oneteam.ai.shared.extensions.paginate
import services.oneteam.ai.shared.permissions.CreateResourcePermissionRequest
import services.oneteam.ai.shared.permissions.DeleteResourcePermissionRequest
import services.oneteam.ai.shared.permissions.PermissionsService
import services.oneteam.ai.shared.permissions.ResourceType

data class WorkspaceSearchCriteria(
    val searchTerm: String? = "", // search by name
    val workspaceId: Workspace.Id? = null
) {
    companion object {
        fun byId(id: Workspace.Id): WorkspaceSearchCriteria {
            return WorkspaceSearchCriteria(workspaceId = id)
        }
    }
}

class WorkspaceRepository(
    private val checks: Checks,
    private val permissionsService: PermissionsService,
//    private val workspaceUserService: WorkspaceUserService,
) {
    val logger: Logger = LoggerFactory.getLogger(javaClass)

    companion object {
        val SORTABLE_FIELDS = SortableFields(
            mapOf(
                "key" to Workspaces.key,
                "name" to Workspaces.name,
                "id" to Workspaces.id
            )
        )
    }

    fun findOne(id: Workspace.Id): Workspace.ForApi {
        return checks.exists(getById(id)) { "Workspace $id not found" }
    }

    fun findOne(key: Workspace.Key): Workspace.ForApi {
        return checks.exists(getByKey(key)) { "Workspace $key not found" }
    }

    fun getById(id: Workspace.Id): Workspace.ForApi? {
        return WorkspaceEntity.find { Workspaces.id eq id.value and (Workspaces.deleted eq false) }.singleOrNull()
            ?.toDTO()
    }

    fun getByKey(key: Workspace.Key): Workspace.ForApi? {
        return WorkspaceEntity.find { Workspaces.key eq key.value and (Workspaces.deleted eq false) }.singleOrNull()
            ?.toDTO()
    }

    fun create(tenant: Tenant, workspace: Workspace.ForCreate, forUserId: User.Id): Workspace.ForApi {

        return permissionsService.createResource(
            CreateResourcePermissionRequest.ForWorkspace(
                forUserId.value.toString(),
                tenant.id,
            )
        ) { id ->
            WorkspaceUserEntity.new {
                // use the ID we got from the sequence
                workspaceId = EntityID(id, Workspaces)
                userId = EntityID(forUserId.value, UserSchema)
                tenantId = tenant.id
                accessLevel = WorkspaceUser.AccessLevel.entries // add all access levels for creator
                status = WorkspaceUser.Status.ACTIVE
            }

            return@createResource WorkspaceEntity.new(id) { // provide the ID
                name = workspace.name.value
                key = workspace.key.value
                description = workspace.description?.value
                documentId = workspace.documentId?.value
                tenantId = tenant.id
            }.toDTO()

        }
    }

    fun update(workspace: Workspace.ForUpdate): Workspace.ForApi {
        return WorkspaceEntity.findByIdAndUpdate(workspace.id.value) {
            it.name = workspace.name.value
            it.documentId = workspace.documentId?.value
            it.description = workspace.description?.value
        }!!.toDTO()
    }

    fun updateDetails(workspace: Workspace.ForUpdateDetails): Workspace.ForApi {
        return WorkspaceEntity.findByIdAndUpdate(workspace.id.value) {
            it.name = workspace.name.value
            it.description = workspace.description?.value
        }!!.toDTO()
    }

    /**
     * Soft delete a workspace by marking it as deleted.
     */
    fun delete(tenant: Tenant, id: Workspace.Id) {
        WorkspaceEntity.findByIdAndUpdate(id.value) {
            it.deleted = true
        }
        // todo delete workspace user rows? or leave them because its a soft delete?
        permissionsService.deleteResource(
            // TODO should we delete all things related to this workspace in spicedb?
            DeleteResourcePermissionRequest(
                ResourceType.WORKSPACE,
                id.value.toString(),
                tenant.id
            )
        )
    }

    fun getAll(): List<Workspace.ForApi> {
        //make the iterator non-lazy
        return WorkspaceEntity.find { Workspaces.deleted eq false }.map { it.toDTO() }
    }

    fun searchByCriteria(
        pageRequest: PageRequest,
        searchCriteria: WorkspaceSearchCriteria
    ): Page<Workspace.ForApi> {
        searchCriteria.toQuery().paginate(pageRequest, SORTABLE_FIELDS) {
            WorkspaceEntity.wrapRow(it)
        }.let {
//            val accessLevels = workspaceUserService.findAccessLevelsByWorkspaceId(workspace.id)
            return Page(
                pageRequest,
                it.total,
                it.items.map { workspaceEntity -> workspaceEntity.toDTO() }
            )
        }
    }
}

/**
 * Workspaces table is restricted by RLS to only those who are linked to a workspace via workspace_users.
 */
fun WorkspaceSearchCriteria.toQuery(): Query {
    val query = Workspaces.selectAll().andWhere { Workspaces.deleted eq false }
    if (searchTerm?.isNotBlank() == true) {
        query.andWhere { Workspaces.name.lowerCase() like "%${searchTerm.lowercase()}%" }
    }

    return query
}
