package services.oneteam.ai.shared.domains.workspace

import kotlinx.serialization.Serializable
import services.oneteam.ai.shared.Checks
import services.oneteam.ai.shared.domains.EntityMetadata
import services.oneteam.ai.shared.domains.Page
import services.oneteam.ai.shared.domains.PageRequest
import services.oneteam.ai.shared.domains.collection.Visibility
import services.oneteam.ai.shared.domains.collection.form.Form
import services.oneteam.ai.shared.domains.collection.form.FormService
import services.oneteam.ai.shared.domains.collection.foundation.Foundation
import services.oneteam.ai.shared.domains.collection.foundation.FoundationService
import services.oneteam.ai.shared.domains.flow.configuration.FlowConfiguration
import services.oneteam.ai.shared.domains.user.User
import services.oneteam.ai.shared.domains.workspace.document.IDocumentService
import services.oneteam.ai.shared.domains.workspace.document.create
import services.oneteam.ai.shared.middlewares.RequestContext
import services.oneteam.ai.shared.otSerializer
import services.oneteam.ai.shared.withTenantTransactionScope
import java.time.Instant
import kotlin.coroutines.coroutineContext

/**
 * Service for managing workspaces.
 *
 * This service will always look up the workspace by its key to ensure it exists,
 * and will throw NotFoundException if it does not, ensuring a 404 response is returned
 * when an invalid workspace is referenced.
 */
class WorkspaceService(
    private val workspaceRepository: WorkspaceRepository,
    private val foundationService: FoundationService,
    private val documentService: IDocumentService,
    private val foundationConfigurationService: FoundationConfigurationService,
    private val workspaceVersionService: WorkspaceVersionService,
    private val formService: FormService,
    private val workspaceUserService: WorkspaceUserService,
    val check: Checks
) {

    suspend fun findAll(): List<Workspace.ForApi> {
        return withTenantTransactionScope {


            val workspaces = workspaceRepository.getAll()
            return@withTenantTransactionScope workspaces.map { workspace ->
                getCurrentUserAccessLevelsForWorkspace(workspace)
            }
        }
    }

    suspend fun getCurrentUserAccessLevelsForWorkspace(workspace: Workspace.ForApi): Workspace.ForApi {
        return withTenantTransactionScope {
            val userId = User.Id(coroutineContext[RequestContext]!!.principalId()!!)
            val workspaceUser = workspaceUserService.findByWorkspaceAndUserId(workspace.id, userId)
            return@withTenantTransactionScope workspace.copy(
                currentUser = Workspace.ForApi.CurrentUser(
                    accessLevels = workspaceUser.accessLevel
                )
            )
        }
    }

    suspend fun search(
        pageRequest: PageRequest, workspaceSearchCriteria: WorkspaceSearchCriteria
    ): Page<Workspace.ForApi> {
        return withTenantTransactionScope {
            val searchResult = workspaceRepository.searchByCriteria(pageRequest, workspaceSearchCriteria)

            val workspacesWithCurrentUser = searchResult.items.map { workspace ->
                getCurrentUserAccessLevelsForWorkspace(workspace)
            }

            return@withTenantTransactionScope Page(
                searchResult.page,
                searchResult.total,
                workspacesWithCurrentUser
            )
        }
    }

    /**
     * Create a new workspace with a default document.
     */
    suspend fun create(
        workspace: Workspace.ForCreate, user: User.Id
    ): Workspace.ForApi {
        return withTenantTransactionScope {
            val workspaceForJson = createDefaultWorkspaceDocument()
            return@withTenantTransactionScope create(workspace, workspaceForJson, user)
        }
    }

    /**
     * Create a new workspace with a given document.
     */
    suspend fun create(
        workspace: Workspace.ForCreate,
        workspaceForJson: Workspace.ForJson,
        userId: User.Id,
        timeoutMillis: Long? = null
    ): Workspace.ForApi {

        return withTenantTransactionScope { tenant ->
            val createdWorkspace = workspaceRepository.create(tenant, workspace, userId)
            // create document
            val documentId = documentService.create(
                tenant.id, workspaceForJson.copy(
                    id = createdWorkspace.id,
                    name = createdWorkspace.name,
                    key = createdWorkspace.key,
                    description = createdWorkspace.description
                ), timeoutMillis = timeoutMillis
            )
            // set document id in workspace entity

            val updated = workspaceRepository.update(
                Workspace.ForUpdate(
                    id = createdWorkspace.id,
                    name = createdWorkspace.name,
                    key = createdWorkspace.key,
                    description = createdWorkspace.description,
                    documentId = Workspace.DocumentId(documentId),
                    configuration = workspaceForJson
                )
            )
            // create foundation with name and key of the workspace and link it to the root foundation configuration
            foundationService.create(
                Foundation.ForCreate(
                    name = Foundation.Name(createdWorkspace.name.value),
                    key = Foundation.Key(createdWorkspace.key.value.uppercase()),
                    workspaceId = createdWorkspace.id,
                    parentId = null,
                    foundationConfigurationId = workspaceForJson.foundations.order[0],
                    visibility = Visibility.INHERIT
                ), skipValidation = true,
                userId = userId
            )

            return@withTenantTransactionScope updated
        }

    }

    /**
     * Create a template workspace document - needs to be updated with workspace entity details.
     */
    suspend fun createDefaultWorkspaceDocument(): Workspace.ForJson {
        return withTenantTransactionScope {
            val rootFoundationConfiguration = foundationConfigurationService.createRootFoundationConfiguration()
            val workspaceForJson = Workspace.ForJson(
                id = Workspace.Id(0),
                name = Workspace.Name(""),
                key = Workspace.Key(""),
                description = Workspace.Description(""),
                foundations = OrderedMap(listOf(rootFoundationConfiguration)),
                forms = emptyMap(),
                flows = OrderedMap<FlowConfiguration.Id, FlowConfiguration.ForJson>(emptyList()),
                series = emptyMap(),
                labels = emptyMap(),
                errors = emptyList(),
                metadata = EntityMetadata(Instant.now(), Instant.now())
            )
            return@withTenantTransactionScope workspaceForJson
        }
    }

    suspend fun update(workspace: Workspace.ForUpdate): Workspace.ForApi {
        return withTenantTransactionScope {
            workspaceRepository.findOne(workspace.id)
            return@withTenantTransactionScope workspaceRepository.update(workspace)
        }
    }

    suspend fun updateDetails(workspace: Workspace.ForUpdateDetails): Workspace.ForApi {
        return withTenantTransactionScope {
            workspaceRepository.findOne(workspace.id)
            val updatedWorkspace = workspaceRepository.updateDetails(workspace)
            val rootFoundation = foundationService.root(workspace.id)
            foundationService.update(
                Foundation.ForUpdate(
                    id = rootFoundation.id,
                    name = Foundation.Name(updatedWorkspace.name.value),
                    key = Foundation.Key(updatedWorkspace.key.value),
                    workspaceId = updatedWorkspace.id,
                    foundationConfigurationId = rootFoundation.foundationConfigurationId,
                    parentId = null,
                )

            )
            return@withTenantTransactionScope updatedWorkspace
        }
    }

    suspend fun delete(id: Workspace.Id) {
        withTenantTransactionScope { tenant ->
            workspaceRepository.findOne(id) // check if exists, 404 if not found
            workspaceRepository.delete(tenant, id)
        }
    }

    suspend fun get(id: Workspace.Id): Workspace.ForApi {
        return withTenantTransactionScope {
            return@withTenantTransactionScope workspaceRepository.findOne(id)
        }
    }

    suspend fun get(key: Workspace.Key): Workspace.ForApi {
        return withTenantTransactionScope {
            return@withTenantTransactionScope checkExistsKey(key)
        }
    }

    suspend fun findByKey(key: Workspace.Key): Workspace.ForApi? {
        return withTenantTransactionScope {
            return@withTenantTransactionScope workspaceRepository.getByKey(key)
        }
    }

    suspend fun findByKeyOrThrow(key: Workspace.Key): Workspace.ForApi? {
        return withTenantTransactionScope {
            return@withTenantTransactionScope check.exists(
                workspaceRepository.getByKey(key), { "Unknown workspace key $key" })
        }
    }

    private fun checkExistsKey(key: Workspace.Key): Workspace.ForApi {
        val entity = workspaceRepository.getByKey(key)
        return check.exists(entity) { "Unknown workspace key $key" }
    }

    suspend fun importWorkspaceConfiguration(id: Workspace.Id, importConfig: String): String {
        return withTenantTransactionScope {
            val workspace = get(id)
            if (workspace.documentId?.value == null) {
                throw IllegalArgumentException("Workspace document not found")
            }

            val parsedContent = otSerializer.decodeFromString<Workspace.ForJson>(importConfig).copy(
                id = Workspace.Id(workspace.id.value),
                key = Workspace.Key(workspace.key.value),
                name = Workspace.Name(workspace.name.value)
            )
            val sanitizedParsedContent = parsedContent.copy(
                variables = parsedContent.variables.mapValues { (_, variable) ->
                    variable.copy(
                        securedRef = null, // always
                        value = if (variable.isSecured == true) {
                            null
                        } else {
                            variable.value
                        }
                    )
                })

            val foundationIdToReplace = sanitizedParsedContent.foundations.order[0]
            val workspaceConfigurationFoundationIdToKeep = (foundationService.root(id)).foundationConfigurationId

            val newContent = otSerializer.encodeToString(Workspace.ForJson.serializer(), sanitizedParsedContent)
                .replace(foundationIdToReplace.value, workspaceConfigurationFoundationIdToKeep.value);
            val parsedNewContent = otSerializer.decodeFromString<Workspace.ForJson>(newContent)

            return@withTenantTransactionScope documentService.update(
                workspace.documentId.value, null, parsedNewContent, "", Workspace.ForJson::class
            )
        }
    }

    suspend fun globalSearchFormsAndFoundations(
        queryParams: GlobalSearchParams,
        workspaceId: Workspace.Id,
        formPageRequest: PageRequest,
        foundationPageRequest: PageRequest
    ): CombinedSearchResult {
        return withTenantTransactionScope {
            val keywords = queryParams.keywords
            if (keywords.isEmpty()) {
                return@withTenantTransactionScope CombinedSearchResult(
                    foundations = emptyList(), forms = emptyList()
                )
            }
            val workspaceConfigurationResults = workspaceVersionService.searchConfigByKeywords(workspaceId, keywords)
            val matchedFoundations = foundationService.searchByKeywords(
                workspaceId, foundationPageRequest, keywords, workspaceConfigurationResults.matchedFoundationConfigs
            )
            var matchedForms: List<Form.ForApi> = emptyList()
            if (workspaceConfigurationResults.matchedFormConfigs.isNotEmpty() || workspaceConfigurationResults.matchedIntervals.isNotEmpty() || keywords.isNotEmpty()) {
                val forms = formService.searchByConfigId(
                    workspaceId,
                    formPageRequest,
                    workspaceConfigurationResults.matchedFormConfigs,
                    workspaceConfigurationResults.matchedIntervals,
                    keywords
                )
                matchedForms = forms.items
            }
            return@withTenantTransactionScope CombinedSearchResult(
                matchedFoundations.items, matchedForms
            )
        }

    }

    suspend fun findInterval(
        workspaceId: Workspace.Id, seriesId: SeriesConfiguration.Id, intervalIdOrName: String
    ): Interval {
        val workspaceConfiguration = workspaceVersionService.findVersion(workspaceId)
        val seriesConfiguration = workspaceConfiguration.configuration.findSeries(seriesId)
        return seriesConfiguration.findIntervalByIdOrName(intervalIdOrName)
    }
}

fun WorkspaceEntity.toDTO(currentUserAccessLevels: List<WorkspaceUser.AccessLevel> = emptyList()): Workspace.ForApi {
    return Workspace.ForApi(
        id = Workspace.Id(this.id.value),
        name = Workspace.Name(this.name),
        key = Workspace.Key(this.key),
        description = this.description?.let { Workspace.Description(it) },
        documentId = this.documentId?.let { Workspace.DocumentId(it) },
        metadata = EntityMetadata.from(this),
        currentUser = Workspace.ForApi.CurrentUser(
            accessLevels = currentUserAccessLevels
        )
    )
}

@Serializable
data class CombinedSearchResult(
    val foundations: List<Foundation.ForApi>, val forms: List<Form.ForApi>
)

@Serializable
data class GlobalSearchParams(
    val keywords: String
)
